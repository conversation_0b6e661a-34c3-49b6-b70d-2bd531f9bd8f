('C:\\Users\\<USER>\\Desktop\\智能命题-0812\\build\\main\\zf智能命题API—0813.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\智能命题-0812\\build\\main\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\智能命题-0812\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\智能命题-0812\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\智能命题-0812\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\智能命题-0812\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\智能命题-0812\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('main', 'C:\\Users\\<USER>\\Desktop\\智能命题-0812\\main.py', 'PYSOURCE')],
 'python311.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
