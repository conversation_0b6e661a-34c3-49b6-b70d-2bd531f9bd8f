# 智能命题系统配置文件
model_service:
  mode: online   # 可选值：offline 或 online
  use_ainvoke: true  # 是否使用 ainvoke 异步方法，false 则使用 invoke
# 模型配置
models:
  default: "deepseek-r1"
  available:
    - name: "deepseek-r1"
      api_base: "https://api.deepseek.com/v1"
      api_key: "${DEEPSEEK_API_KEY}"
      max_tokens: 4000
      temperature: 0.7
    - name: "gpt-4"
      api_base: "https://api.openai.com/v1"
      api_key: "${OPENAI_API_KEY}"
      max_tokens: 4000
      temperature: 0.7
    - name: "qwen-plus"
      api_base: "https://dashscope.aliyuncs.com/api/v1"
      api_key: "${DASHSCOPE_API_KEY}"
      max_tokens: 4000
      temperature: 0.7
    - name: "local"
      api_base: "http://172.168.0.200:8082/v1"
      api_key: "${DASHSCOPE_API_KEY}"
      max_tokens: 4000
      temperature: 0.7

# 试题克隆配置
question_clone:
  # 克隆策略
  strategies:
    full: "完全克隆：保持原题结构，只变更试题内容"
    content_only: "仅克隆内容：保持所有属性，只改变QuesStr"
    structure_only: "仅克隆结构：保持题型和属性，清空具体内容"
  default_grade: "高中生"
  
  # 组合题克隆步骤
  composite_steps:
    - "分析组合题结构"
    - "克隆材料部分"
    - "克隆题干结构"
    - "生成子题目内容"
    - "组装完整试题"
  
  # 基础题克隆步骤
  basic_steps:
    - "分析题目结构"
    - "生成新试题内容"
    - "验证克隆质量"

# 提示词模板
prompts:
  # 基础题克隆提示词
  basic_clone: |
    ## 角色与任务

    你是一个专业的试题克隆专家。请基于以下原题进行克隆，生成一道新的试题。

    ## 原题信息

    - **题目内容**：{original_content}
    - **题目类型**：{question_type}
    - **学科**：{subject}
    - **受试人群**：{grade}
    - **难度**：{difficulty}
    - **知识点**：{knowledge_points}

    ## 克隆要求

    1. **保持结构一致**：保持相同的题型结构和难度
    2. **内容创新**：使用不同的具体内容（如不同的材料、不同的题目描述）
    3. **知识点对应**：保持相同的知识点要求
    4. **质量保证**：确保试题质量不低于原题
    5. **学科特色**：生成的内容要符合{subject}学科的特点

    ## 输出试题结构

    ```
    {mapped_structure}
    ```

    ## 输出要求

    1. **内容纯净**：不要输出任何与试题结构无关的内容
    2. **严格遵循**：严格按照输出试题结构的要求生成试题

    ---

    **请生成新的试题内容：**

  # 组合题材料克隆提示词
  composite_material_clone: |
    ## 角色与任务

    你是一个专业的试题材料克隆专家。请基于以下原题材料进行克隆，生成新的材料内容。

    ## 原材料信息

    - **材料内容**：{original_material}
    - **学科**：{subject}
    - **年级**：{grade}
    - **难度**：{difficulty}

    ## 克隆要求

    1. **类型一致**：保持相同的材料类型和长度
    2. **内容创新**：使用不同的具体内容
    3. **难度对应**：保持相同的难度和知识点要求
    4. **质量保证**：确保材料质量不低于原题

    ## 输出要求

    1. **内容纯净**：不要输出任何与材料内容无关的内容
    2. **严格遵循**：严格按照输出材料内容的要求生成材料内容

    ---

    **请生成新的材料内容：**

  # 组合题子题目克隆提示词
  composite_subquestion_clone: |
    ## 角色与任务

    你是一个专业的子题目克隆专家。请基于以下原题子题目进行克隆，生成新的子题目内容。

    ## 原子题目信息

    - **子题目内容**：{original_subquestion}
    - **题目类型**：{subquestion_type}
    - **材料背景**：{material_context}
    - **学科**：{subject}
    - **原子题结构**：{original_content}

    ## 克隆要求

    1. **类型一致**：保持相同的题目类型和分值
    2. **材料关联**：基于材料背景生成相应的子题目
    3. **知识点对应**：保持相同的知识点要求
    4. **质量保证**：确保题目质量不低于原题
    5. **结构严格**：严格按照结构要求生成内容
    6. **内容完整**：生成的内容应该是完整的子题目，包含题目描述和要求

    ## 输出试题结构

    ```
    {mapped_structure}
    ```

    ## 输出要求

    1. **内容纯净**：不要输出任何与试题结构无关的内容
    2. **严格遵循**：严格按照输出试题结构的要求生成试题

    ---

    **请生成新的子题目内容：**

  # 基础题命题提示词
  single_question_prompt: |
    ## 角色与任务

    你是一个专业的**{exam_subject}试题命制专家**。请基于给定的试题材料生成试题。

    ## 试题信息

    - **试题类型**：{ques_type_name}
    - **难度要求**：{difficulty}
    - **知识点**：{knowledge_points}
    - **命制策略**：{strategy}

    ## 命题具体要求


    {ques_requirements}


    ## 具体工作要求

    请基于上述材料生成符合要求的试题，确保：

    1. **属性符合**：符合给定的题型和属性要求
    2. **难度准确**：难度符合要求
    3. **知识覆盖**：知识点覆盖准确
    4. **解析完整**：试题解析包括考核要点、解题思路和正确答案三部分。例如：本题考核系统管理员岗位职责在实际情景中的应用。根据考点工作人员岗位设置要求中的信息，系统管理员负责“管理机房、监考机、考试机等硬件设施设备，保障网络通畅”以及“负责考试软件安装、测试和技术培训”。当考试机软件出现故障时，应由系统管理员负责处理。选项A考场监考员主要负责主持考试和维护考场秩序；选项B流动监考员主要负责巡查楼层各考场；选项D视频监考员主要负责考试期间各考场视频监控、巡查工作。故选C。

    ## 输出要求

    1. 不要输出任何与试题无关的内容，注意只需要输出试题，不要输出试题等文字内容
    2. 确保试题符合命题要求。
    3. 注意严格按照输出试题结构的命制试题，不要将结构的内容作为试题内容。
    4. 注意不要使用markdown格式输出。

    【输出试题结构】
    {structure_content}

    输出：
  
  # 组合题材料命制提示词
  build_material_generation_prompt: |
    ## 角色与任务

    你是一个专业的**{exam_subject}试题材料生成专家**。请基于以下规划生成高质量的试题材料。

    ## 题目信息

    - **题目类型**：{base_name}
    - **考试科目**：{exam_subject}

    ## 材料生成策略


    {material_task}


    ## 具体工作要求

    请生成符合要求的试题材料，材料应该：

    1. **策略符合**：符合生成策略的要求
    2. **内容丰富**：内容丰富，适合作为组合题的基础
    3. **学科特色**：符合{exam_subject}学科特点
    4. **支撑性强**：能够支撑多个子题目的生成

    ## 输出要求
    1. 不要输出任何与材料无关的内容，注意只需要输出材料，不要输出试题材料等文字内容；
    2. 确保材料符合材料生成策略的要求；
    3. 所有策略要符合{exam_subject}学科特点；
    4. 禁止使用Markdown格式输出。

    输出：

  # 组合题子题命题
  build_single_subquestion_prompt: |
    ## 角色与任务

    你是一个专业的**{exam_subject}试题命制专家**。请基于给定的试题材料生成子题。

    ## 试题材料


    {generated_material}


    ## 子题信息

    - **子题类型**：{child_type}
    - **难度要求**：{difficulty}
    - **知识点**：{knowledge_points}
    - **题目数量**：{count}道
    - **命制策略**：{strategy}

    ## 命题具体要求


    {ques_requirements}


    ## 具体工作要求

    请基于上述材料生成符合要求的子题，确保：

    1. 子题与材料内容紧密相关，并与子题的知识点紧密相关
    2. 符合给定的题型和属性要求
    3. 难度符合要求
    4. 知识点覆盖准确
    5. 试题解析包括考核要点、解题思路和正确答案三部分。例如：本题考核系统管理员岗位职责在实际情景中的应用。根据考点工作人员岗位设置要求中的信息，系统管理员负责“管理机房、监考机、考试机等硬件设施设备，保障网络通畅”以及“负责考试软件安装、测试和技术培训”。当考试机软件出现故障时，应由系统管理员负责处理。选项A考场监考员主要负责主持考试和维护考场秩序；选项B流动监考员主要负责巡查楼层各考场；选项D视频监考员主要负责考试期间各考场视频监控、巡查工作。故选C。

    【输出要求】
    1. 不要输出任何与子题无关的内容，注意只需要输出子题，不要输出试题子题等文字内容；
    2. 确保子题符合命题要求。
    3. 质量保证措施要切实有效；
    4. 所有策略要符合{exam_subject}学科特点。
    5. 注意严格按照输出试题结构的命制试题，不要将结构的内容作为子题内容。
    6. 输出格式上，注意禁止使用markdown格式输出。

    【输出试题结构】
    {structure_content}

    输出：
    
# 系统配置
system:
  # 日志级别
  log_level: "INFO"
  
  # 任务超时时间（秒）
  task_timeout: 300
  
  # 流式数据推送间隔（秒）
  stream_interval: 1
  
  # 最大并发任务数
  max_concurrent_tasks: 10

# 错误码配置
error_codes:
  # 业务错误码
  business:
    INVALID_QUESTION_DATA: {"code": 4001, "msg": "无效的试题数据"}
    CLONE_FAILED: {"code": 4002, "msg": "试题克隆失败"}
    MODEL_ERROR: {"code": 4003, "msg": "模型调用失败"}
    TIMEOUT_ERROR: {"code": 4004, "msg": "任务超时"}
  
  # HTTP状态码
  http:
    SUCCESS: 200
    BAD_REQUEST: 400
    UNAUTHORIZED: 401
    FORBIDDEN: 403
    NOT_FOUND: 404
    INTERNAL_ERROR: 500 