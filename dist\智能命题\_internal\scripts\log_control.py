#!/usr/bin/env python3
"""
日志控制脚本
用于快速切换日志级别和查看日志统计
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime, timedelta
import re

def set_environment(env_type):
    """设置环境变量"""
    if env_type == "dev":
        os.environ["ENVIRONMENT"] = "development"
        print("✅ 已切换到开发环境模式")
        print("   - 控制台输出：DEBUG级别及以上")
        print("   - 文件输出：DEBUG级别及以上")
    elif env_type == "prod":
        os.environ["ENVIRONMENT"] = "production"
        print("✅ 已切换到生产环境模式")
        print("   - 控制台输出：WARNING级别及以上")
        print("   - 文件输出：INFO级别及以上")
    else:
        print("❌ 无效的环境类型，请使用 'dev' 或 'prod'")
        return False
    
    print("\n💡 提示：重启应用后生效")
    return True

def show_log_stats():
    """显示日志统计信息"""
    log_file = "logs/app.log"
    error_log_file = "logs/error.log"
    
    if not os.path.exists(log_file):
        print("❌ 日志文件不存在：", log_file)
        return
    
    print("📊 日志统计信息")
    print("=" * 50)
    
    # 统计今天的日志
    today = datetime.now().strftime("%Y-%m-%d")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        today_lines = [line for line in lines if today in line]
        
        # 统计各级别日志数量
        info_count = len([line for line in today_lines if "| INFO" in line])
        warning_count = len([line for line in today_lines if "| WARNING" in line])
        error_count = len([line for line in today_lines if "| ERROR" in line])
        debug_count = len([line for line in today_lines if "| DEBUG" in line])
        
        print(f"📅 今日日志统计 ({today}):")
        print(f"   🔍 DEBUG:   {debug_count:>6} 条")
        print(f"   ℹ️  INFO:    {info_count:>6} 条")
        print(f"   ⚠️  WARNING: {warning_count:>6} 条")
        print(f"   ❌ ERROR:   {error_count:>6} 条")
        print(f"   📝 总计:    {len(today_lines):>6} 条")
        
        # 文件大小
        file_size = os.path.getsize(log_file) / 1024 / 1024  # MB
        print(f"\n📁 文件信息:")
        print(f"   📄 主日志文件: {file_size:.2f} MB")
        
        if os.path.exists(error_log_file):
            error_file_size = os.path.getsize(error_log_file) / 1024 / 1024
            print(f"   🚨 错误日志文件: {error_file_size:.2f} MB")
        
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")

def tail_logs(log_type="all", lines=20):
    """查看最新日志"""
    log_file = "logs/app.log"
    
    if not os.path.exists(log_file):
        print("❌ 日志文件不存在：", log_file)
        return
    
    print(f"📖 最新 {lines} 条日志 ({log_type}):")
    print("=" * 80)
    
    try:
        if log_type == "error":
            # 只显示错误日志
            cmd = f"grep 'ERROR' {log_file} | tail -{lines}"
        elif log_type == "warning":
            # 显示警告和错误日志
            cmd = f"grep -E '(WARNING|ERROR)' {log_file} | tail -{lines}"
        else:
            # 显示所有日志
            cmd = f"tail -{lines} {log_file}"
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        else:
            print("📭 没有找到相关日志")
            
    except Exception as e:
        print(f"❌ 查看日志失败: {e}")

def clean_old_logs(days=30):
    """清理旧日志"""
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        print("❌ 日志目录不存在")
        return
    
    cutoff_date = datetime.now() - timedelta(days=days)
    cleaned_count = 0
    
    print(f"🧹 清理 {days} 天前的日志文件...")
    
    for filename in os.listdir(logs_dir):
        if filename.endswith('.log'):
            filepath = os.path.join(logs_dir, filename)
            try:
                file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                if file_time < cutoff_date:
                    os.remove(filepath)
                    cleaned_count += 1
                    print(f"   🗑️  删除: {filename}")
            except Exception as e:
                print(f"   ❌ 删除失败 {filename}: {e}")
    
    if cleaned_count == 0:
        print("   ✅ 没有需要清理的日志文件")
    else:
        print(f"   ✅ 已清理 {cleaned_count} 个日志文件")

def monitor_errors():
    """实时监控错误日志"""
    log_file = "logs/app.log"
    
    if not os.path.exists(log_file):
        print("❌ 日志文件不存在：", log_file)
        return
    
    print("🔍 实时监控错误日志 (按 Ctrl+C 退出):")
    print("=" * 50)
    
    try:
        cmd = f"tail -f {log_file} | grep --line-buffered -E '(WARNING|ERROR)'"
        subprocess.run(cmd, shell=True)
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
    except Exception as e:
        print(f"❌ 监控失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="智能命题系统日志控制工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 设置环境命令
    env_parser = subparsers.add_parser("env", help="设置日志环境")
    env_parser.add_argument("type", choices=["dev", "prod"], help="环境类型")
    
    # 统计命令
    subparsers.add_parser("stats", help="显示日志统计信息")
    
    # 查看日志命令
    tail_parser = subparsers.add_parser("tail", help="查看最新日志")
    tail_parser.add_argument("--type", choices=["all", "error", "warning"], 
                           default="all", help="日志类型")
    tail_parser.add_argument("--lines", type=int, default=20, help="显示行数")
    
    # 清理日志命令
    clean_parser = subparsers.add_parser("clean", help="清理旧日志")
    clean_parser.add_argument("--days", type=int, default=30, help="保留天数")
    
    # 监控命令
    subparsers.add_parser("monitor", help="实时监控错误日志")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == "env":
        set_environment(args.type)
    elif args.command == "stats":
        show_log_stats()
    elif args.command == "tail":
        tail_logs(args.type, args.lines)
    elif args.command == "clean":
        clean_old_logs(args.days)
    elif args.command == "monitor":
        monitor_errors()

if __name__ == "__main__":
    main()
